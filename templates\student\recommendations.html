{% extends 'student/base.html' %}

{% block title %}Course Recommendations - Course Recommendation System{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">

    <!-- Header -->
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900">Course Recommendations</h1>
        <p class="mt-2 text-gray-600">Personalized course suggestions based on your academic history, interests, and career goals.</p>
    </div>

    <!-- Profile Incomplete Warning -->
    {% if profile_incomplete %}
    <div class="bg-gradient-to-r from-amber-50 to-orange-50 border border-amber-200 rounded-xl p-8 mb-8">
        <div class="text-center">
            <div class="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-amber-100 mb-4">
                <svg class="h-8 w-8 text-amber-600" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                </svg>
            </div>
            <h2 class="text-2xl font-bold text-amber-800 mb-4">Complete Your Profile to Unlock Recommendations</h2>
            <p class="text-amber-700 mb-6 max-w-2xl mx-auto">
                To provide you with the most accurate and personalized course recommendations, please complete all sections of your student profile.
            </p>

            <!-- Missing Components -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-8">
                <!-- Academic Records -->
                <div class="flex items-center p-4 {% if profile_completion.academic_records %}bg-green-100 border-green-300{% else %}bg-white border-amber-300{% endif %} border rounded-lg">
                    <div class="flex-shrink-0 mr-3">
                        {% if profile_completion.academic_records %}
                        <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                        </div>
                        {% else %}
                        <div class="w-8 h-8 bg-amber-500 rounded-full flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                            </svg>
                        </div>
                        {% endif %}
                    </div>
                    <div class="flex-1 text-left">
                        <h3 class="text-sm font-medium {% if profile_completion.academic_records %}text-green-800{% else %}text-amber-800{% endif %}">
                            Academic Records
                        </h3>
                        <p class="text-xs {% if profile_completion.academic_records %}text-green-600{% else %}text-amber-600{% endif %}">
                            {% if profile_completion.academic_records %}✓ Completed{% else %}Add your course history{% endif %}
                        </p>
                    </div>
                    {% if not profile_completion.academic_records %}
                    <a href="{% url 'academic_records' %}" class="text-amber-600 hover:text-amber-700">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                        </svg>
                    </a>
                    {% endif %}
                </div>

                <!-- Interests -->
                <div class="flex items-center p-4 {% if profile_completion.interests %}bg-green-100 border-green-300{% else %}bg-white border-amber-300{% endif %} border rounded-lg">
                    <div class="flex-shrink-0 mr-3">
                        {% if profile_completion.interests %}
                        <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                        </div>
                        {% else %}
                        <div class="w-8 h-8 bg-amber-500 rounded-full flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                            </svg>
                        </div>
                        {% endif %}
                    </div>
                    <div class="flex-1 text-left">
                        <h3 class="text-sm font-medium {% if profile_completion.interests %}text-green-800{% else %}text-amber-800{% endif %}">
                            Academic Interests
                        </h3>
                        <p class="text-xs {% if profile_completion.interests %}text-green-600{% else %}text-amber-600{% endif %}">
                            {% if profile_completion.interests %}✓ Completed{% else %}Set your interests{% endif %}
                        </p>
                    </div>
                    {% if not profile_completion.interests %}
                    <a href="{% url 'student_interests' %}" class="text-amber-600 hover:text-amber-700">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                        </svg>
                    </a>
                    {% endif %}
                </div>

                <!-- Career Goals -->
                <div class="flex items-center p-4 {% if profile_completion.career_goals %}bg-green-100 border-green-300{% else %}bg-white border-amber-300{% endif %} border rounded-lg">
                    <div class="flex-shrink-0 mr-3">
                        {% if profile_completion.career_goals %}
                        <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                        </div>
                        {% else %}
                        <div class="w-8 h-8 bg-amber-500 rounded-full flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                            </svg>
                        </div>
                        {% endif %}
                    </div>
                    <div class="flex-1 text-left">
                        <h3 class="text-sm font-medium {% if profile_completion.career_goals %}text-green-800{% else %}text-amber-800{% endif %}">
                            Career Goals
                        </h3>
                        <p class="text-xs {% if profile_completion.career_goals %}text-green-600{% else %}text-amber-600{% endif %}">
                            {% if profile_completion.career_goals %}✓ Completed{% else %}Define your goals{% endif %}
                        </p>
                    </div>
                    {% if not profile_completion.career_goals %}
                    <a href="{% url 'career_goals' %}" class="text-amber-600 hover:text-amber-700">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                        </svg>
                    </a>
                    {% endif %}
                </div>

                <!-- Admission Test -->
                <div class="flex items-center p-4 {% if profile_completion.admission_test %}bg-green-100 border-green-300{% else %}bg-white border-amber-300{% endif %} border rounded-lg">
                    <div class="flex-shrink-0 mr-3">
                        {% if profile_completion.admission_test %}
                        <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                        </div>
                        {% else %}
                        <div class="w-8 h-8 bg-amber-500 rounded-full flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                            </svg>
                        </div>
                        {% endif %}
                    </div>
                    <div class="flex-1 text-left">
                        <h3 class="text-sm font-medium {% if profile_completion.admission_test %}text-green-800{% else %}text-amber-800{% endif %}">
                            Admission Test
                        </h3>
                        <p class="text-xs {% if profile_completion.admission_test %}text-green-600{% else %}text-amber-600{% endif %}">
                            {% if profile_completion.admission_test %}✓ Completed{% else %}Take assessment{% endif %}
                        </p>
                    </div>
                    {% if not profile_completion.admission_test %}
                    <a href="{% url 'admission_test_start' %}" class="text-amber-600 hover:text-amber-700">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                        </svg>
                    </a>
                    {% endif %}
                </div>

                <!-- Survey -->
                <div class="flex items-center p-4 {% if profile_completion.survey %}bg-green-100 border-green-300{% else %}bg-white border-amber-300{% endif %} border rounded-lg">
                    <div class="flex-shrink-0 mr-3">
                        {% if profile_completion.survey %}
                        <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                        </div>
                        {% else %}
                        <div class="w-8 h-8 bg-amber-500 rounded-full flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                            </svg>
                        </div>
                        {% endif %}
                    </div>
                    <div class="flex-1 text-left">
                        <h3 class="text-sm font-medium {% if profile_completion.survey %}text-green-800{% else %}text-amber-800{% endif %}">
                            Learning Survey
                        </h3>
                        <p class="text-xs {% if profile_completion.survey %}text-green-600{% else %}text-amber-600{% endif %}">
                            {% if profile_completion.survey %}✓ Completed{% else %}Share preferences{% endif %}
                        </p>
                    </div>
                    {% if not profile_completion.survey %}
                    <a href="{% url 'student_survey' %}" class="text-amber-600 hover:text-amber-700">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                        </svg>
                    </a>
                    {% endif %}
                </div>
            </div>

            <!-- Action Button -->
            <div class="text-center">
                <a href="{% url 'student_dashboard' %}"
                   class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-lg text-white bg-gradient-to-r from-amber-500 to-orange-500 hover:from-amber-600 hover:to-orange-600 transition-all duration-200 hover:shadow-lg transform hover:-translate-y-0.5">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                    </svg>
                    Go to Dashboard
                </a>
            </div>
        </div>
    </div>
    {% else %}

    <!-- Top 3 Recommendations -->
    {% if recommendations %}
    <div class="glassmorphism rounded-2xl p-8 mb-8">
        <div class="text-center mb-8">
            <h2 class="text-3xl font-bold text-gray-900 mb-2">Your Top Course Recommendations</h2>
            <p class="text-gray-600">Based on your academic profile, interests, and career goals</p>
            {% if showing_top_3 and total_recommendations > 3 %}
            <p class="text-sm text-gray-500 mt-2">Showing top 3 of {{ total_recommendations }} recommendations</p>
            {% endif %}
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {% for rec in recommendations %}
            <div class="glassmorphism rounded-xl p-6 hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1 relative">
                <!-- Ranking Badge -->
                <div class="absolute -top-3 -left-3 w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-bold text-sm shadow-lg">
                    {{ forloop.counter }}
                </div>

                <!-- Confidence Score -->
                <div class="flex justify-between items-start mb-4">
                    <div class="flex-1">
                        <h3 class="text-xl font-bold text-gray-900 mb-1">{{ rec.course.code }}</h3>
                        <h4 class="text-lg font-semibold text-gray-700 mb-2">{{ rec.course.name }}</h4>
                    </div>
                    <div class="text-right">
                        <div class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium
                            {% if rec.confidence_score >= 0.8 %}bg-green-100 text-green-800
                            {% elif rec.confidence_score >= 0.6 %}bg-yellow-100 text-yellow-800
                            {% else %}bg-blue-100 text-blue-800{% endif %}">
                            {{ rec.confidence_score|floatformat:0 }}% Match
                        </div>
                    </div>
                </div>

                <!-- Course Details -->
                <p class="text-gray-600 mb-4 leading-relaxed">{{ rec.course.description|truncatewords:25 }}</p>

                <div class="flex items-center justify-between text-sm text-gray-500 mb-4">
                    <span class="flex items-center">
                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                        </svg>
                        {{ rec.course.credits }} Credits
                    </span>
                    <span class="flex items-center">
                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                        </svg>
                        {{ rec.course.department.code }}
                    </span>
                    <span class="capitalize px-2 py-1 rounded text-xs
                        {% if rec.course.difficulty == 'beginner' %}bg-green-100 text-green-700
                        {% elif rec.course.difficulty == 'intermediate' %}bg-yellow-100 text-yellow-700
                        {% else %}bg-red-100 text-red-700{% endif %}">
                        {{ rec.course.difficulty }}
                    </span>
                </div>

                <!-- Reasoning -->
                {% if rec.reasoning %}
                <div class="glassmorphism rounded-lg p-4 mb-4">
                    <h5 class="text-sm font-semibold text-gray-800 mb-2 flex items-center">
                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                        </svg>
                        Why This Course?
                    </h5>
                    <p class="text-sm text-gray-700 leading-relaxed">{{ rec.reasoning }}</p>
                </div>
                {% endif %}

                <!-- Action Buttons -->
                <div class="flex space-x-3">
                    <button class="flex-1 bg-gradient-to-r from-blue-500 to-purple-600 text-white font-semibold py-3 px-4 rounded-lg hover:from-blue-600 hover:to-purple-700 transition-all duration-300 transform hover:scale-105 shadow-md">
                        View Course Details
                    </button>
                    <button class="p-3 glassmorphism rounded-lg hover:shadow-md transition-all duration-300 group">
                        <svg class="w-5 h-5 text-gray-600 group-hover:text-blue-600 transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z"></path>
                        </svg>
                    </button>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
    {% else %}
    <!-- No Recommendations -->
    <div class="glassmorphism rounded-2xl p-12 text-center">
        <svg class="mx-auto h-16 w-16 text-gray-400 mb-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
        </svg>
        <h3 class="text-xl font-semibold text-gray-900 mb-2">No Recommendations Available</h3>
        <p class="text-gray-600 mb-8 max-w-md mx-auto">We're working on generating your personalized course recommendations. This may take a moment.</p>
        <div class="flex justify-center space-x-4">
            <a href="{% url 'academic_records' %}"
               class="inline-flex items-center px-6 py-3 bg-gradient-to-r from-blue-500 to-purple-600 text-white font-semibold rounded-lg hover:from-blue-600 hover:to-purple-700 transition-all duration-300 transform hover:scale-105 shadow-md">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                </svg>
                Add Academic Records
            </a>
            <a href="{% url 'student_interests' %}"
               class="inline-flex items-center px-6 py-3 glassmorphism text-gray-700 font-semibold rounded-lg hover:shadow-md transition-all duration-300">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                </svg>
                Add Interests
            </a>
        </div>
    </div>
    {% endif %}
    {% endif %}
</div>
{% endblock %}
